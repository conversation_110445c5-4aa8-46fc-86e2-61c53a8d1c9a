<?php

namespace App\Http\Controllers;

use App\Models\PetHealthRecord;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PetHealthRecordController extends Controller
{
    public function index()
    {
        $records = PetHealthRecord::where('user_id', Auth::id())->get();
        return view('pet_health.index', compact('records'));
    }

    public function create()
    {
        return view('pet_health.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'pet_name' => 'required',
            'species' => 'required',
        ]);

        PetHealthRecord::create([
            'user_id' => Auth::id(),
            'pet_name' => $request->pet_name,
            'species' => $request->species,
            'breed' => $request->breed,
            'medical_history' => $request->medical_history,
            'last_vaccination' => $request->last_vaccination,
        ]);

        return redirect()->route('pet_health.index')->with('success', 'Record saved.');
    }
}
