

<?php $__env->startSection('content'); ?>
    <h2 class="form-title">Add Pet Health Record</h2>

    <form method="POST" action="<?php echo e(route('pet_health.store')); ?>" class="health-form">
        <?php echo csrf_field(); ?>

        <div class="form-group">
            <label for="pet_name">Pet Name</label>
            <input type="text" name="pet_name" id="pet_name" placeholder="e.g. Buddy" required>
        </div>

        <div class="form-group">
            <label for="species">Species</label>
            <input type="text" name="species" id="species" placeholder="e.g. Dog" required>
        </div>

        <div class="form-group">
            <label for="breed">Breed</label>
            <input type="text" name="breed" id="breed" placeholder="e.g. Golden Retriever">
        </div>

        <div class="form-group">
            <label for="medical_history">Medical History</label>
            <textarea name="medical_history" id="medical_history" rows="3" placeholder="e.g. Dewormed, Allergy to chicken"></textarea>
        </div>

        <div class="form-group">
            <label for="last_vaccination">Last Vaccination Date</label>
            <input type="date" name="last_vaccination" id="last_vaccination">
        </div>

        <button type="submit" class="submit-button">💾 Save Record</button>
    </form>

    <style>
        .form-title {
            font-size: 26px;
            color: #2c3e50;
            margin-bottom: 25px;
        }

        .health-form {
            max-width: 500px;
            background: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #34495e;
        }

        input[type="text"],
        input[type="date"],
        textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ccc;
            border-radius: 6px;
            font-size: 15px;
        }

        textarea {
            resize: vertical;
        }

        .submit-button {
            width: 100%;
            padding: 12px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .submit-button:hover {
            background-color: #2980b9;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\pawportal\resources\views/pet_health/create.blade.php ENDPATH**/ ?>