<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\PetHealthRecordController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\Auth\PasswordController;
use App\Http\Controllers\Auth\EmailVerificationNotificationController;

Route::get('/', function () {
    return redirect('/login');
});

// USER ROUTES
Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
Route::post('/login', [AuthController::class, 'login']);
Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
Route::post('/register', [AuthController::class, 'register']);

// DASHBOARD ROUTE WITH AUTH MIDDLEWARE
Route::middleware(['auth'])->group(function () {
    Route::get('/dashboard', function () {
    return view('dashboard');
        })->name('dashboard');


    // PROFILE ROUTES
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // PASSWORD UPDATE ROUTE
    Route::put('/password', [PasswordController::class, 'update'])->name('password.update');

    // EMAIL VERIFICATION ROUTE
    Route::post('/email/verification-notification', [EmailVerificationNotificationController::class, 'store'])
        ->middleware('throttle:6,1')
        ->name('verification.send');

    // PET HEALTH RECORD ROUTES
    Route::get('/pet-health', [PetHealthRecordController::class, 'index'])->name('pet_health.index');
    Route::get('/pet-health/create', [PetHealthRecordController::class, 'create'])->name('pet_health.create');
    Route::post('/pet-health', [PetHealthRecordController::class, 'store'])->name('pet_health.store');

    // ADDITIONAL ROUTES
    Route::get('/pet-adoption', fn() => view('adoption.index'))->name('pet_adoption');
    Route::get('/online-consultation', fn() => view('consultation.index'))->name('online_consultation');
    Route::get('/lost-found', fn() => view('lostfound.index'))->name('lost_found');
    Route::get('/multi-pet', fn() => view('multipet.index'))->name('multi_pet');
});

// ADMIN ROUTES
Route::get('/admin/login', [AdminController::class, 'showLogin'])->name('admin.login');
Route::post('/admin/login', [AdminController::class, 'login']);
Route::get('/admin/dashboard', [AdminController::class, 'dashboard'])
    ->middleware(['auth', 'admin'])
    ->name('admin.dashboard');

// LOGOUT
Route::post('/logout', function () {
    Auth::logout();
    return redirect()->route('login')->with('success', 'You have been logged out.');
})->name('logout');